import {
  httpClient,
  wwwUrl
} from "/utils/http";
import {
  APP_GCASH
} from "/utils/config";
import {generateRandomString } from '/utils/common-utils';

// 常量定义
const CONSTANTS = {
  APP_ID: APP_GCASH.appId,
  SUCCESS_CODE: 200,
  TOAST_DURATION: 2000,
  JUMP_TYPES: {
    PARENT: "parent",
    EXTERNAL: "external",
    PROMOS: "promos",
  },
};

/**
 * 外部链接跳转对话框组件
 * 负责处理各种类型的链接跳转，包括父应用链接、外部链接和H5链接
 */
Component({
  mixins: [],
  data: {
    // 对话框显示状态
    showOpenLinkDialog: false,
  },

  props: {
    // 用户token
    token: String,

    // 跳转信息对象
    jumpTypeInfo: Object,

    // 是否显示跳转确认对话框
    showJumpConfrim_1: Boolean,

    // 关闭回调函数
    onClose: Function,
  },
  /**
   * 组件挂载时初始化对话框状态
   */
  didMount() {
    this.updateDialogVisibility();
  },

  /**
   * 组件更新时同步对话框状态
   */
  didUpdate(prevProps) {
    // 只在 showJumpConfrim_1 发生变化时更新
    if (prevProps.showJumpConfrim_1 !== this.props.showJumpConfrim_1) {
      this.updateDialogVisibility();
    }
  },

  /**
   * 组件卸载时清理资源
   */
  didUnmount() {
    // 清理可能的定时器或监听器
  },
  methods: {
    /**
     * 更新对话框可见性
     */
    updateDialogVisibility() {
      this.setData({
        showOpenLinkDialog: this.props.showJumpConfrim_1,
      });
    },

    /**
     * 验证跳转信息
     * @param {Object} jumpTypeInfo - 跳转信息对象
     * @returns {boolean} 验证结果
     */
    validateJumpTypeInfo(jumpTypeInfo) {
      if (!jumpTypeInfo || typeof jumpTypeInfo !== "object") {
        console.error("Invalid jumpTypeInfo: must be an object");
        return false;
      }

      if (!jumpTypeInfo.type) {
        console.error("Invalid jumpTypeInfo: type is required");
        return false;
      }

      if (jumpTypeInfo.type === CONSTANTS.JUMP_TYPES.PARENT && !jumpTypeInfo.url) {
        console.error("Invalid jumpTypeInfo: url is required for parent type");
        return false;
      }

      return true;
    },

    /**
     * 显示错误提示
     * @param {string} message - 错误消息
     */
    showErrorToast(message) {
      my.showToast({
        type: "fail",
        content: message || "error",
        duration: CONSTANTS.TOAST_DURATION,
      });
    },

    /**
     * 取消跳转确认
     */
    cancelJumpConfrim_1_done() {
      if (typeof this.props.onClose === "function") {
        this.props.onClose();
      } else {
        console.warn("onClose callback is not a function");
      }
    },

    /**
     * 打开父应用链接
     */
    openParentLink() {
      if (!this.validateJumpTypeInfo(this.props.jumpTypeInfo)) {
        this.showErrorToast("Jump information invalid");
        return;
      }

      const {
        url
      } = this.props.jumpTypeInfo;

      my.call("navigateToLink", {
        targetLink: url,
        appId: CONSTANTS.APP_ID,
        success: (res) => {
          console.log("父应用链接跳转成功:", res);
          // 跳转成功后关闭对话框
          this.cancelJumpConfrim_1_done();
        },
        fail: (res) => {
          console.error("父应用链接跳转失败:", res);
          this.showErrorToast(res);
        },
      });
    },

    /**
     * 构建目标链接
     * @param {string} gcashToken - GCash认证token
     * @returns {string} 构建的目标链接
     */
    buildTargetLink(gcashToken) {
      const {
        jumpTypeInfo
      } = this.props;

      if (jumpTypeInfo.type === CONSTANTS.JUMP_TYPES.EXTERNAL) {
        return jumpTypeInfo.url;
      }
      // 获取一个唯一值
      const timestamp = Date.now() + generateRandomString(4);
      let targetLink = `${wwwUrl}?g_auth_code=${gcashToken}&type=${jumpTypeInfo.type}&timestamp=${timestamp}`;

      // 添加ID参数（promos类型除外）
      if (jumpTypeInfo.id && jumpTypeInfo.type !== CONSTANTS.JUMP_TYPES.PROMOS) {
        targetLink += `&id=${jumpTypeInfo.id}`;
      }

      return targetLink;
    },

    /**
     * 获取GCash认证token
     * @returns {Promise<string>} GCash token
     */
    async getGCashToken() {
      const token = this.props.token || this.props.jumpTypeInfo.token;

      if (!token) {
        throw new Error("Token is required");
      }

      const response = await httpClient.requestWithToken(
        "api/player/login/gcash_token",
        token, {}, {
          baseUrl: "common",
          showLoading: true,
        }
      );

      if (response.data.code !== CONSTANTS.SUCCESS_CODE) {
        throw new Error(response.data.msg || "fail to load redeem record");
      }

      return response.data.data;
    },

    /**
     * 执行链接跳转
     * @param {string} targetLink - 目标链接
     */
    navigateToLink(targetLink) {
      return new Promise((resolve, reject) => {
        my.call("navigateToLink", {
          targetLink: targetLink,
          appId: CONSTANTS.APP_ID,
          success: (res) => {
            console.log("链接跳转成功:", res);
            // 跳转成功后关闭对话框
            this.cancelJumpConfrim_1_done();
            resolve(res);
          },
          fail: (res) => {
            console.error("链接跳转失败:", res);
            this.showErrorToast(res.errorMessage||"error");
          },
        });
      });
    },

    /**
     * 确认跳转（主方法）
     */
    async confirmJumpWithType() {
      // 验证跳转信息
      if (!this.validateJumpTypeInfo(this.props.jumpTypeInfo)) {
        this.showErrorToast("Jump information invalid");
        return;
      }

      // 如果是父应用类型，直接跳转
      if (this.props.jumpTypeInfo.type === CONSTANTS.JUMP_TYPES.PARENT) {
        this.openParentLink();
        return;
      }
      console.log("开始H5链接跳转:", this.props.jumpTypeInfo);

      try {
        // 获取GCash认证token
        const gcashToken = await this.getGCashToken();

        // 构建目标链接
        const targetLink = this.buildTargetLink(gcashToken);
        console.log("目标链接:", targetLink);

        // 执行跳转
        await this.navigateToLink(targetLink);
      } catch (error) {
        console.error("链接跳转过程中发生错误:", error);
        this.showErrorToast(error.message || "Jump failed.");
      }
    },
  },
});