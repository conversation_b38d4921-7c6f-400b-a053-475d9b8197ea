/**
 * 通用工具函数集合
 * 从 pages/game/list.js 中抽取的常用工具函数
 */

// ============================================================================
// 格式化相关工具函数
// ============================================================================

/**
 * 格式化货币显示
 * @param {number|string} number - 要格式化的数字
 * @param {boolean} needDecimal - 是否需要保留小数，默认true
 * @param {boolean} needSign - 是否需要显示正负号，默认false
 * @returns {string} 格式化后的货币字符串
 */
export function formatCurrency(number, needDecimal = true, needSign = false) {
  if (typeof number !== "number") {
    if (typeof number === "string") {
      number = Number(number);
      if (isNaN(number)) return "----";
    } else {
      return "----";
    }
  }

  if (isNaN(number) || !isFinite(number)) return "----";

  let numStr = needDecimal ? (number / 100).toFixed(2) : number.toFixed(0);
  const parts = numStr.split(".");
  let integerPart = parts[0];
  const decimalPart = parts.length > 1 ? "." + parts[1] : "";

  let formattedIntegerPart = "";
  const integerLength = integerPart.length;
  for (let i = 0; i < integerLength; i++) {
    formattedIntegerPart += integerPart[i];
    if ((integerLength - 1 - i) % 3 === 0 && i !== integerLength - 1 && integerPart[i] !== "-") {
      formattedIntegerPart += ",";
    }
  }

  if (needSign && number >= 0) {
    formattedIntegerPart = "+" + formattedIntegerPart;
  }

  return formattedIntegerPart + decimalPart;
}

/**
 * 千分位格式化
 */
export function thousandFormat(x) {
  return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

/**
 * 千分金额格式转数字
 */
export function formattedAmountToNumber(formattedAmount) {
  if (typeof formattedAmount !== "string") {
    throw new TypeError("输入必须是一个字符串");
  }
  const numberString = formattedAmount.replace(/[^\d.-]/g, "");
  const number = parseFloat(numberString);
  if (isNaN(number)) {
    throw new Error("无法将输入转换为有效的数字");
  }
  return number;
}

/**
 * 格式化日期时间
 */
export function formatDateTime(dateTimeStr) {
  const date = new Date(dateTimeStr.replace(" ", "T"));
  const formattedDate = date.toLocaleDateString("en-US", {
    month: "short",
    day: "2-digit",
    year: "numeric",
  });
  const formattedTime = date.toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: true,
  });
  return `${formattedDate} ${formattedTime}`;
}

/**
 * 获取月份英文缩写
 */
export function getMonthName(month) {
  if (!month) return "";
  const date = new Date(2023, month - 1, 1);
  return date.toLocaleString("en-US", { month: "short" });
}

/**
 * 数字格式化（去除末尾0）
 * @param {number} num - 要格式化的数字
 * @param {number} digits - 小数位数，默认2位
 * @returns {string} 格式化后的字符串
 */
export function numFormat3(num, digits = 2) {
  // 正则表达式：去除末尾多余的 0 和小数点
  var rx = /\.0+$|(\.[0-9]*[1-9])0+$/;
  // 使用 toFixed 固定小数位数，并去除多余的 0
  return parseFloat(num.toFixed(digits)).toString().replace(rx, "$1");
}

/**
 * 数字格式化（带单位）
 * @param {number} num - 要格式化的数字
 * @param {number} digits - 小数位数，默认2位
 * @returns {string} 格式化后的字符串（如：1.2K, 3.4M）
 */
export function numFormat2(num, digits = 2) {
  var si = [
    { value: 1, symbol: "" },
    { value: 1e3, symbol: "K" },
    { value: 1e6, symbol: "M" },
    { value: 1e9, symbol: "B" },
  ];
  var rx = /\.0+$|(\.[0-9]*[1-9])0+$/;
  var i;
  for (i = si.length - 1; i > 0; i--) {
    if (num >= si[i].value) {
      break;
    }
  }
  return (num / si[i].value).toFixed(digits).replace(rx, "$1") + si[i].symbol;
}

/**
 * 数组排序（按金额）
 */
export function sortByDate(a, b) {
  return Number(a.amount) - Number(b.amount);
}

// ============================================================================
// 手机号码处理工具函数
// ============================================================================

/**
 * KYC页面手机号掩码处理
 */
export function maskPhoneNumberKYC(phoneNumber) {
  if (!phoneNumber || typeof phoneNumber !== "string") return phoneNumber;
  return phoneNumber.slice(0, -7) + "****" + phoneNumber.slice(-3);
}

/**
 * 普通手机号掩码处理
 */
export function maskPhoneNumber(phoneNumber) {
  if (!phoneNumber || typeof phoneNumber !== "string") return phoneNumber;
  return phoneNumber.slice(0, -6) + "****" + phoneNumber.slice(-2);
}

// ============================================================================
// 文本和HTML处理工具函数
// ============================================================================

/**
 * 去除HTML标签和常见HTML实体
 */
export function stripHtml(html) {
  if (!html) return "";
  return html
    .replace(/<[^>]*>/g, "")
    .replace(/&nbsp;/g, " ")
    .replace(/&amp;/g, "&")
    .replace(/&lt;/g, "<")
    .replace(/&gt;/g, ">")
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/&apos;/g, "'")
    .replace(/\s+/g, " ")
    .trim();
}

/**
 * 处理位运算
 */
export function getBitValue(num, position) {
  if (Number.isInteger(num) && num >= -2147483648 && num <= 2147483647 && Number.isInteger(position) && position >= 0 && position <= 31) {
    return (num >> (position - 1)) & 1;
  } else {
    console.error("Invalid parameters");
    return -1;
  }
}

/**
 * 复制文本到剪贴板
 */
export function copyToClipboard(text, onSuccess, onFail) {
  my.setClipboard({
    text: text || "",
    success: (res) => {
      my.showToast({
        type: "success",
        content: "copy success",
        duration: 2000,
      });
      if (onSuccess) onSuccess(res);
    },
    fail: (res) => {
      my.showToast({
        type: "fail",
        content: "copy failed",
        duration: 2000,
      });
      if (onFail) onFail(res);
    },
  });
}

/**
 * 格式化状态名称
 */
export function formatStatusName(status_name) {
  if (!status_name) return "";
  return status_name
    .replace(/cashback/gi, (match) => (match[0] === "C" ? "Rebate" : "rebate"))
    .replace(/payday/gi, "Bonus Time")
    .replace(/casino/gi, "Live Table");
}

// ============================================================================
// 数据处理工具函数
// ============================================================================

/**
 * 按日期分组数据
 */
export function groupByDate(list) {
  const grouped = {};

  list.forEach((item) => {
    let dateStr;
    if (item.created_at) {
      dateStr = item.created_at.split(" ")[0];
    }
    if (item.updated_at) {
      dateStr = item.updated_at.split(" ")[0];
    }

    const date = new Date(dateStr);
    const formattedDate = date.toLocaleDateString("en-US", {
      month: "short",
      day: "2-digit",
      year: "numeric",
    });

    if (!grouped[formattedDate]) {
      grouped[formattedDate] = {
        time: formattedDate,
        data: [],
      };
    }

    grouped[formattedDate].data.push(item);
  });

  return Object.values(grouped);
}

/**
 * 处理提现数据
 */
export function handleWithdrawalDatas(list) {
  const newList = [...list];
  for (let index = 0; index < newList.length; index++) {
    const item = newList[index];
    item.coins = "-" + formatCurrency(item.total_amount);
    item.status_name = item.status_desc;
    item.recharge_status = item.ship_status;
    item.labStatus = "Withdrawal";

    if ([3].includes(item.ship_status)) {
      item.status_name = item.err_msg || "Unsuccessful";
      item.color = "status-failure";
      continue;
    }
    if ([1].includes(item.ship_status)) {
      item.color = "status-success";
      continue;
    }
    if ([0, 2].includes(item.ship_status)) {
      item.color = "status-pending";
      continue;
    }

    if (item.sys_remark == "Adjustment") {
      item.labStatus = "Transfer";
    }
  }
  return newList;
}

// ============================================================================
// 验证工具函数
// ============================================================================

/**
 * 验证数值范围
 */
export function validateRange(value, min, max) {
  const numValue = Number(value);
  if (isNaN(numValue)) {
    return { isValid: false, message: "Invalid number format" };
  }
  if (numValue < min || numValue > max) {
    return { isValid: false, message: `Value range: ${min}-${max}` };
  }
  return { isValid: true, message: "" };
}

/**
 * 验证是否为正整数
 */
export function validatePositiveInteger(value) {
  const numValue = Number(value);
  if (isNaN(numValue)) {
    return { isValid: false, message: "Must be a number" };
  }
  if (!Number.isInteger(numValue)) {
    return { isValid: false, message: "Must be an integer" };
  }
  if (numValue <= 0) {
    return { isValid: false, message: "Must be a positive number" };
  }
  return { isValid: true, message: "" };
}

// ============================================================================
// 其他工具函数
// ============================================================================

/**
 * 安全的JSON解析
 */
export function safeJsonParse(jsonStr, defaultValue = null) {
  try {
    return JSON.parse(jsonStr);
  } catch (error) {
    console.warn("JSON parsing failed:", error);
    return defaultValue;
  }
}

/**
 * 生成随机字符串
 */
export function generateRandomString(length = 8, chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789") {
  let result = "";
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 检查字符串是否为空或只包含空白字符
 */
export function isBlank(str) {
  return !str || typeof str !== "string" || str.trim().length === 0;
}

/**
 * 防抖函数
 */
export function debounce(func, wait, immediate) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) func(...args);
  };
}

/**
 * 节流函数
 */
export function throttle(func, limit) {
  let inThrottle;
  return function (...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}
