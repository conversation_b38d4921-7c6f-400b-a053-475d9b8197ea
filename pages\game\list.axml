<!-- topBar -->
<!-- {{userInfo.is_vip ? 'windowtitle-vip': ''}} -->
<view class="page-list">
  <view class="flex-tops">
    <view style="padding-top: {{navPaddingTop}};" class="windowtitle windowtitle-vip">
      <image mode="widthFix" style="width: 80px;" src="../../assets/new-icons/Logo.svg" />
    </view>

    <view class="notification">
      <image style="width: 24rpx;" class="icon" mode="widthFix" src="../../assets/new-icons/sound.png" />
      <view class="notif-container">
        <text class="notif-message" style="animation: marquee {{marqueeDuration}} linear infinite">
          {{notificationText}}
        </text>
      </view>
    </view>
  </view>

  <!-- 内容  -->
  <!-- {{userInfo.is_vip ? 'container-vip': ''}} -->
  <view class="container container-vip {{showTransactionMask?'not-scroll':''}} ">
    <!-- 轮播 -->
    <view class="swiper-container">
      <swiper
        indicator-dots="true"
        indicator-color="rgba(0, 0, 0, .4)"
        indicator-active-color="#fff"
        autoplay="true"
        interval="3000"
        class="swiper-content"
      >
        <swiper-item a:if="{{ bannerList.length<1 }}">
          <image
            onLoad="handleBannerLoad"
            onTap="playGameWithType"
            data-type="promos"
            data-id="1"
            mode="widthFix"
            defaultSource="../../assets/new-icons/loading.png"
            src="../../assets/new-icons/banner.jpg"
          />
        </swiper-item>
        <swiper-item a:for="{{bannerList}}" a:for-item="item" a:else>
          <image
            onLoad="handleBannerLoad"
            onTap="playGameWithType"
            data-type="{{item.jump_type}}"
            data-id="{{item.id}}"
            mode="widthFix"
            defaultSource="../../assets/new-icons/loading.png"
            src="{{item.url}}"
          />
        </swiper-item>
      </swiper>
    </view>
    <!-- 用户信息 -->
    <view class="{{userInfo.is_vip ? 'isvip' : 'novip'}} wallet-call">
      <view class="wallet-top">
        <view a:if="{{userInfo.is_vip}}">
          <image
            onTap="playGameWithType"
            mode="widthFix"
            data-type="vip"
            style="width: 59px;"
            src="../../assets/new-icons/viplogo.png"
          />
        </view>
        <view a:else>
          <image
            onTap="playGameWithType"
            mode="widthFix"
            data-type="vip"
            style="width: 59px;"
            src="../../assets/new-icons/novip.png"
          />
        </view>
        <image
          onTap="showUserInfo"
          mode="widthFix"
          style="width: 81px;"
          src="../../assets/new-icons/infologo.png"
        />
      </view>

      <view class="wallet-content">
        <view class="wallet-header">
          <text>Total Balance (P)</text>
          <text onTap="getPopupTransactionRecords" class="transaction-records">
            Transaction
            <image mode="widthFix" style="width: 12rpx;" src="../../assets/new-icons/arrow-right.png" />
          </text>
        </view>
        <view class="balance">
          <text>
            {{userInfo.balance}}
          </text>
          <text class="amount-icon">P</text>
          <view
            animation="{{animation}}"
            onTap="handleRefreshIconClick"
            class="balance-refresh"
          >
          </view>

        </view>

      </view>
      <view class="wallet-bottom">
        <!-- 验证风控 -->
        <view onTap="showRiskDialog" class="withdraw">
          <image mode="widthFix" src="../../assets/new-icons/withdrawbtn.png" />
          <text>Withdrawal</text>
        </view>
        <view onTap="showDepositMask" class="topup" mode="widthFix">
          <image mode="widthFix" src="../../assets/new-icons/topupbtn.png" />
          <text>Top-Up</text>
        </view>
      </view>
    </view>


    <!-- 分类 -->
    <view class="gamelist-box">
      <view class="gamelist-title">
        <text>Game Categories</text>
      </view>
      <view class="gamestypelist">
        <image
          mode="widthFix"
          onTap="playGameWithType"
          data-type="game"
          data-id="10006"
          src="../../assets/new-icons/luyan/table.png"
        />
        <image
          mode="widthFix"
          onTap="playGameWithType"
          data-type="game"
          data-id="10000"
          src="../../assets/new-icons/luyan/spin.png"
        />
        <image
          mode="widthFix"
          onTap="playGameWithType"
          data-type="game"
          data-id="10001"
          src="../../assets/new-icons/luyan/card.png"
        />
        <image
          mode="widthFix"
          onTap="playGameWithType"
          data-type="game"
          data-id="10002"
          src="../../assets/new-icons/luyan/arcade.png"
        />
        <image
          mode="widthFix"
          onTap="playGameWithType"
          data-type="game"
          data-id="10004"
          src="../../assets/new-icons/luyan/bingo.png"
        />
        <image
          mode="widthFix"
          onTap="playGameWithType"
          data-type="game"
          data-id="10005"
          src="../../assets/new-icons/luyan/sports.png"
        />
      </view>

      <!-- 监管logo -->
      <view class="regulatory-logo">
        <image mode="widthFix" src="../../assets/new-icons/launchlogo.png" />
        <view>|</view>
        <image mode="widthFix" src="../../assets/new-icons/logo_link_1.png" />
        <view>|</view>
        <image mode="widthFix" src="../../assets/new-icons/logo_link_2.png" />
        <!--<image mode="widthFix" src="../../assets/nav-bottom.png" /> -->
      </view>
    </view>

    <!-- 按钮 -->
    <view
      class="explore-games-button"
      style=""
      data-index="222"
      onTap="playGameWithType"
      data-type="all"
    >
      <view class="allgame-btn">All Games</view>
    </view>

    <!-- 客服 -->
    <view onTap="playGameWithType" data-type="service" class="service-game">
      <image mode="scaleToFill" src="../../assets/new-icons/cervice.png" />
    </view>

    <!-- 下载引导浮标 -->
    <view
      a:if="{{downloadGuideConfig.show}}"
      class="download-guide-float bottom-right"
      onTap="onDownloadGuideClick"
    >
      <image
        mode="scaleToFill"
        src="../../assets/download-btn.svg"
        class="download-guide-icon"
      />
      <view a:if="{{downloadGuideConfig.content}}" class="download-guide-tooltip">
        {{downloadGuideConfig.content}}
      </view>
    </view>
  </view>
</view>
<!-- 活动弹窗 -->
<activity-pops
  a:if="{{token}}"
  info="{{activityPopsInfo}}"
  deposit_award="{{deposit_award}}"
  onCloseDepositPopup="onCloseDepositPopup"
  onUpdateAvailableCoins="setAvailableCoins"
/>
<!--

-->
<!-- 个人信息弹框 -->
<view class="mask {{showUserInfoMask? 'show': ''}} " onTap="offUserInfo"></view>
<view class="deposit-box {{showUserInfoMask?'show':''}}">
  <view class="transaction-box-title detail-title">
    <text>Personal Info</text>
    <view class="detail-title-close">
      <image onTap="offUserInfo" mode="scaleToFill" src="../../assets/new-icons/close.png" />
    </view>
  </view>
  <view class="userinfo-content">
    <view class="user-info-item">
      <view>
        User ID
      </view>
      <view class="user-id-input">
        <input disabled="{{true}}" value="{{userInfo.user_id}}" placeholder="" />
        <image
          onTap="handleCopy"
          data-text="{{userInfo.user_id}}"
          class="user-id-copy"
          src="../../assets/new-icons/copy.svg"
        />
      </view>
    </view>
    <view class="user-info-item">
      <view>
        Full legal first and middle name
      </view>
      <input
        disabled="{{true}}"
        value="{{KYCuserInfo.first_middle_name}}"
        placeholder=""
      />
    </view>
    <view class="user-info-item">
      <view>
        Full legal last name
      </view>
      <input disabled="{{true}}" value="{{KYCuserInfo.last_name}}" placeholder="" />
    </view>
    <view class="user-info-item">
      <view>
        Phone number
      </view>
      <input disabled="{{true}}" value="{{KYCuserInfo.phone}}" placeholder="" />
    </view>
    <view style="margin-bottom:100rpx;" class="user-info-item">
      <view>
        Date of birth
      </view>
      <view class="user-inputs">
        <input
          disabled="{{true}}"
          value="{{KYCuserInfo.day}}"
          style="width: 20%;"
          placeholder=""
        />
        <input
          disabled="{{true}}"
          value="{{KYCuserInfo.month_en}}"
          style="width: 40%"
          placeholder=""
        />
        <input
          disabled="{{true}}"
          value="{{KYCuserInfo.year}}"
          style="width: 25%"
          placeholder=""
        />
      </view>
    </view>
  </view>
</view>

<!-- 提现弹窗 -->
<view class="mask {{showRedeemMask?'show':''}}" onTap="offRedeemMask">
</view>
<view class="transaction-box {{showRedeemMask?'show':''}}">
  <view class="transaction-box-title detail-title">
    Withdrawal
    <view class="detail-title-close">
      <image onTap="offRedeemMask" mode="scaleToFill" src="../../assets/new-icons/close.png" />
    </view>
  </view>
  <view class="wallet-main">
    <view class="rule-message justspace">
      <view class="amout-line">
        <!--<image onTap="openMessageValue" mode="scaleToFill" src="../../assets/new-icons/Vector.svg" /> -->
        <view a:if="{{isMessageOpen}}" onTap="closeMessage" class="message-box">
          Generally Receive Top-Up in 5 minutes;
          If your Top-Up has not been received
          exceed 5 minutes,
          please contact
          customer service and provide your
          paymentreceipt.
          <view onTap="closeMessage" class="shadowbox"></view>
        </view>
      </view>

    </view>

    <view class="wallet-balance">
      <image mode="scaleToFill" src="../../assets/wallet.png" />
      <text>
        Your Wallet Balance(P) : {{userInfo.balance}}
      </text>
    </view>
    <!-- luyan -->
    <view class="price-selection">
      <view
        a:for="{{redeemList.tags}}"
        a:for-item="item"
        class="price-select-item {{index == selectDepositIndex?'price-select-item-active':''}}"
        a:key="*this"
        onTap="selectRedeem"
        data-index="{{index}}"
      >
        {{item.showAmount}}
      </view>
    </view>
    <view class="price-input {{validInputValue  ? '' : 'red-input'}}">
      <input
        value="{{ depositValue}}"
        onInput="bindRedeemValue"
        type="number"
        placeholder="Enter Amount: {{redeemList.min || 0}} - {{redeemList.max || 0}}"
      />
      <text class="amount-icon">P</text>
    </view>
    <view a:if="{{ !validInputValue }}" class="clienterror">
      {{validInputValueText}}
    </view>
  </view>
  <view a:if="{{ !validInputValue || !depositValue }}" class="price-continue">
    Continue
  </view>
  <view
    a:if="{{ validInputValue && depositValue && !redeemList.isForbid }}"
    onTap="confrimDepositOrWithdraw"
    class="price-continue active-continue"
  >
    Continue
  </view>
</view>

<!-- 充值弹框 -->
<view class="mask {{showDepositMask?'show':''}}" onTap="offDepositMask"></view>
<view class="transaction-box {{showDepositMask?'show':''}}">
  <view class="transaction-box-title detail-title">
    Top-Up
    <view class="detail-title-close">
      <image onTap="offDepositMask" mode="scaleToFill" src="../../assets/new-icons/close.png" />
    </view>
  </view>
  <view class="wallet-main">
    <view class="rule-message justspace">
      <view class="amout-line">
        <!-- Amount -->
        <!--<image onTap="openMessageValue" mode="scaleToFill" src="../../assets/new-icons/Vector.svg" /> -->
        <view a:if="{{isMessageOpen}}" onTap="closeMessage" class="message-box">
          Generally Receive Top-Up in 5 minutes;
          If your Top-Up has not been received
          exceed 5 minutes,
          please contact
          customer service and provide your
          paymentreceipt.
          <view onTap="closeMessage" class="shadowbox"></view>
        </view>
      </view>

    </view>
    <view class="wallet-balance">
      <image mode="scaleToFill" src="../../assets/wallet.png" />
      <text>
        Your Wallet Balance(P) : {{userInfo.balance}}
      </text>
    </view>
    <!-- luyan -->
    <view class="price-selection">
      <view
        a:for="{{depositList.tags}}"
        a:for-item="item"
        class="price-select-item {{index == selectDepositIndex?'price-select-item-active':''}}"
        a:key="*this"
        onTap="selectDeposit"
        data-index="{{index}}"
      >
        {{item.showAmount}}
        <view a:if="{{is_first_charge}}" class="first-recharge-award">
          +{{item.award}}
        </view>
      </view>
    </view>
    <view class="price-input {{validInputValue  ? '' : 'red-input'}}">
      <input
        maxlength="17"
        value="{{ depositValue}}"
        onInput="bindDepositValue"
        type="number"
        placeholder="Enter Amount: {{depositList.min || 0}} - {{depositList.max || 0}}"
      />
      <text class="amount-icon">P</text>
    </view>
    <view a:if="{{ !validInputValue }}" class="clienterror">
      {{validInputValueText}}
    </view>
    <view a:if="{{is_first_charge}}" class="price-bonus">
      <view class="price-item">
        <text>Total(P):</text>
        <view a:if="{{depositValue}}">{{ totalDepositValue || '--'}}</view>
        <view a:else></view>
      </view>
      <view class="price-item">
        <text>Bonus(P):</text>
        <view class="glod">{{depositAward}}</view>
      </view>
    </view>
  </view>

  <view a:if="{{ !validInputValue || !depositValue }}" class="price-continue">
    Continue
  </view>
  <!-- class: before-checkbox-btn -->
  <view
    a:if="{{ validInputValue && depositValue && !depositList.isForbid }}"
    onTap="confrimDepositOrWithdraw"
    class="price-continue active-continue"
  >
    Continue
  </view>
  <!-- 是否批次充值 -->
  <!--<checkbox-group class="topUpBatches-checkbox-group" onChange="clickTopUpBatches"><label><checkbox
        style="background:{{topUpBatchColor}}"
        class="checkbox-round"
        color="#ffffff"
        value="1"
        checked="{{topUpBatched}}"
      />
      Continuous Top-Up</label></checkbox-group> -->
</view>


<!-- 充值提现小窗 -->
<view class="mask-one {{showDepositOneMask?'show':''}}" onTap="offDepositOneMask">
</view>
<view class="deposit-confirm-box {{showDepositOneMask?'show':''}}">
  <view class="transaction-box-title detail-title">
    {{showRedeemMask ? 'Withdrawal' : 'Top-Up'}}
    <view class="detail-title-close">
      <image onTap="offDepositOneMask" mode="scaleToFill" src="../../assets/new-icons/close.png" />
    </view>
  </view>
  <view class="deposit-confim-main confirm-main-margintop">
    <view class="confim-value">{{depositCurrencyValue}} P</view>
    <view class="confim-item justspace">
      <view>Type</view>
      <view class="boldfont">Gcash</view>
    </view>
    <view a:if="{{is_first_charge && showDepositMask}}" class="confim-item justspace">
      <view>Bonus</view>
      <view class="boldfont deposit-award font-din-medium">{{depositAward}} P</view>
    </view>
    <view class="confim-item justspace">
      <view>Total</view>
      <view class="boldfont font-din-medium">{{totalDepositValue}} P</view>
    </view>
  </view>
  <view onTap="doDeposit" class="price-continue active-continue">
    Confirm and Submit
  </view>
</view>

<!-- 消息确认弹框， congratulations -->
<view class="mask-one flexcenter {{showMessageConfrim?'flex':'hiddenno'}}">
  <view class="messageConfrim">
    <view class="confrim-title">
      <view style="width: 48rpx;height:1rpx;"></view>
      <view>{{showMessageTitle}}</view>
      <image onTap="offConfrimMask" mode="scaleToFill" src="../../assets/new-icons/close.png" />
    </view>
    <view class="confrim-param">
      {{showMessagePagrm}}
    </view>
    <view a:if={{showMessageTitle == 'System Maintenance'}} class="confirm-flexs">
      <view onTap="exitMiniProgram" class="confrim-done">Exit</view>
    </view>
    <view a:else class="confirm-flexs">
      <button
        a:if={{showMessageTitle == 'Login Expired'}}
        onTap="exitMiniProgram"
        class="confirm-cancel"
      >
        Exit
      </button>
      <view onTap="DoneConfrimMask" class="confrim-done">
        {{showMessageTitle == 'Login Expired' ? 'ReTry' : showMessageBtnText||'Done'}}
      </view>
    </view>
  </view>
</view>
<!-- 跳转确认弹框  -->
<!--<view class="mask-one flexcenter {{showJumpConfrim_1?'flex':'hiddenno'}}"><view class="messageConfrim"><view class="confrim-title"><view style="width: 48rpx;height:1rpx;"></view><view>Notice</view><image onTap="cancelJumpConfrim_1_done" mode="scaleToFill" src="../../assets/new-icons/close.png" /></view><view class="confrim-param">
      You are now leaving the GCash app. GCash is not responsible for any content or transactions on this websites. We advise to never share your GCash MPIN and OTP.</view><view class="confirm-flexs"><view style="margin-top: 0;" onTap="confirmJumpWithType" class="confrim-done">Got it</view></view></view></view> -->
<!-- 跳转链接弹框 -->
<openlink-dialog
  token="{{token}}"
  jumpTypeInfo="{{jumpTypeInfo}}"
  onClose="offOpenLinkDialog"
  showJumpConfrim_1="{{showJumpConfrim_1}}"
/>
<!-- 未满21岁检测  -->
<view class="mask-one flexcenter {{ is_21_years_old ?'flex':'hiddenno'}}">
  <view class="messageConfrim">
    <view class="confrim-title dialog-title-main">
      <view>Responsible Gaming</view>
    </view>
    <!--<view class="dialog-title-top">
      IMPORTANT NOTICE</view> -->
    <view class="dialog-scroll">
      <view class="dialog-title-content">
        <text style="margin-bottom: 20rpx;">
          By clicking "I agree all", you confirm that you are:
        </text>
        <view>
          <text>·</text>
          <text>Over 21 years old.</text>
        </view>
        <view>
          <text>·</text>
          <text>Not a government official.</text>
        </view>
        <view>
          <text>·</text>
          <text>Not a Gaming Employment License holder.</text>
        </view>
        <view>
          <text>·</text>
          <text>Not a member of the Armed Forces of the Philippines, including the Army, Navy, Air Force, or Philippine National Police.</text>
        </view>
        <view>
          <text>·</text>
          <text>Not on PAGCOR's National Database of Restricted Persons.</text>
        </view>
        <view>
          <text>·</text>
          <text>I have read and agree to the
            <text onTap="openTerms" class="dialog-title-content-link">
              Terms of Services
            </text>
          </text>
        </view>
        <!--<text style="margin-top: 20rpx;">Funds or credits on the account of player who is found ineligible to play shall mean forfeiture of said funds/ credits in favor of the Government.</text> -->
      </view>
      <view class="dialog-error-content">
        <image mode="scaleToFill" src="../../assets/new-icons/dialog-warning.png" />
        <view class="dialog-error-content-text">
          Ineligible player funds will be forfeited to the Government.
        </view>
      </view>
    </view>
    <view class="dialog-logos">
      <view
        onTap="linkto"
        data-url="https://www.pagcor.ph/regulatory/responsible-gaming.php"
        class="dialog-itembox"
      >
        <image mode="scaleToFill" src="../../assets/new-icons/logo_link_1.png" />
      </view>
      <view class="logo-line"></view>
      <view
        onTap="linkto"
        data-url="https://www.pagcor.ph/regulatory/responsible-gaming.php"
        class="dialog-itembox"
      >
        <image style="width: 222rpx;" mode="scaleToFill" src="../../assets/new-icons/logo_link_2.png" />
      </view>
    </view>
    <view class="confirm-flexs">
      <button onTap="openForbidAccess" class="confirm-cancel">Exit</button>
      <view onTap="agreeAll" class="confrim-done">I AGREE ALL</view>
    </view>
  </view>
</view>

<!-- 未满21岁禁止访问  -->
<view class="flex-zindex {{ forbid_access ?'flex':'hiddenno'}}">
  <view class="mask-one flexcenter {{ forbid_access ?'flex':'hiddenno'}}">
    <view class="messageConfrim">
      <view class="confrim-title dialog-title-main">
        <view>Notice</view>
      </view>
      <view class="dialog-title-top small-font">
        You did not agree with our statement, unfortunately we are unable to provide you with services
      </view>
      <view class="confirm-flexs">
        <view onTap="forbidAccessDone" class="confrim-done">DONE</view>
      </view>
    </view>
  </view>
</view>
<!-- 账户锁定  -->
<view class="flex-zindex {{ account_locked ?'flex':'hiddenno'}}">
  <view class="mask-one flexcenter {{ account_locked ?'flex':'hiddenno'}}">
    <view class="messageConfrim">
      <view class="confrim-title dialog-title-main">
        <view>Login Lock</view>
      </view>
      <view class="dialog-title-top small-font">
        {{account_locked_msg}}
        <view class="email-text">{{customer_email}}</view>
      </view>
      <view class="confirm-flexs">
        <view onTap="copyAccountLockedMsg" class="confrim-done">Copy</view>
      </view>
    </view>
  </view>
</view>

<!-- 未满21岁禁止访问 无按钮  -->
<view class="flex-zindex {{ forbid_access_no_button ?'flex':'hiddenno'}}">
  <view class="mask-one flexcenter {{ forbid_access_no_button ?'flex':'hiddenno'}}">
    <view class="messageConfrim">
      <view class="confrim-title dialog-title-main">
        <view>Tips</view>
      </view>
      <view class="dialog-title-top small-font">
        You are under 21 years old. We can not provide you with services. please click on the upper right corner to leave
      </view>
    </view>
  </view>
</view>
<!-- IP限制 无按钮  -->
<view class="flex-zindex {{ ip_restricted ?'flex':'hiddenno'}}">
  <view class="mask-one flexcenter {{ ip_restricted ?'flex':'hiddenno'}}">
    <view class="messageConfrim messageConfrim_ipset">
      <view class="confrim-title dialog-title-main">
        <view>Item Not Available</view>
      </view>
      <view class="dialog-title-top small-font ipforbid">
        Sorry，This service isn’t availablein your regin yet.
        <view>We apologize for the inconvenience caused to you.</view>
      </view>
    </view>
  </view>
</view>

<!-- 交易弹窗中的选择金额弹窗 -->
<view class="mask-one {{showRedeemOneMask?'show':''}}" onTap="offRedeemOneMask">
</view>
<view class="deposit-confirm-box {{showRedeemOneMask?'show':''}}">
  <view class="deposit-confirm-content">
    You are redeeming {{selectedRedeemName}} by {{selectedRedeemCoins}} coins
  </view>
  <view class="deposit-confirm-button" onTap="doRedeem">
    <image class="deposit-confirm-button-image" src="../../assets/confirm-button.png" />
  </view>
</view>

<!-- 交易记录弹窗 -->
<view class="mask {{showTransactionMask?'show':''}}" onTap="offTransactionMask">
</view>
<view class="transaction-box {{showTransactionMask?'show':''}}">
  <view class="transaction-box-title detail-title">
    Transactions
    <view class="detail-title-close">
      <image onTap="offTransactionMask" mode="scaleToFill" src="../../assets/new-icons/close.png" />
    </view>
  </view>
  <view class="picker-list">
    <view
      a:for="{{ transactionTypeArray }}"
      a:for-item="item"
      id="{{ item.id }}"
      onTap="changeActivePicker"
      class="picker-item {{activePicker === item.id ? 'activePickClass' : ''}}"
    >
      {{item.name}}
    </view>
  </view>
  <view class="picker-list time-selects">
    <!--<view onTap="openTimerSelect" class="picker-item">{{transactionDateArray[activeTimer].name}}</view> -->
    <view a:if="{{ activePicker!= 2 }}" onTap="openStatusSelect" class="status-picker picker-item picker-red">
      {{activeStatusText}}
      <image mode="widthFix" style="width: 12px;margin-left: 8px;" src="../../assets/new-icons/arrow-down.png" />
    </view>
    <!--<view 
      a:for="{{ transactionDateArray }}" 
      id="{{ item.id }}"
      a:for-item="item"
      onTap="changeActiveTimer"
      class="picker-item {{activeTimer === item.id ? 'activeTimeClass' : ''}}">{{item.name}}</view> -->
  </view>

  <view class="transaction-select-list-area">
    <scroll-view scroll-y="{{true}}" onScrollToLower="handleTransactionScrollToLower">
      <view
        a:for="{{transactionHistoryList}}"
        a:for-item="times"
        a:key="*this"
        :data-item="item"
      >
        <view class="history-times">
          <view class="history-title-time">{{times.time}}</view>
          <view a:for="{{times.data}}" a:for-item="item" class="
              history-item 
              {{item.color}}
            " a:key="*this" onTap="openDetail" data-item="{{item}}">
            <view a:if="{{ activePicker == 0 }}" class="history-icon">
              <image
                a:if="{{ item.pay_channel == 'Adjustment' }}"
                mode="scaleToFill"
                src="../../assets/new-icons/gift.png"
              />
              <image a:else mode="scaleToFill" src="../../assets/new-icons/{{item.payment_method != 'Gcash' ? 'Maya_Logo' : 'GCash_logo'}}.png" />
            </view>
            <view a:if="{{ activePicker == 1 }}" class="history-icon">
              <image
                a:if="{{ item.sys_remark == 'Adjustment' }}"
                mode="scaleToFill"
                src="../../assets/new-icons/gift.png"
              />
              <image a:else mode="scaleToFill" src="../../assets/new-icons/{{item.payment_method != 'Gcash' ? 'Maya_Logo' : 'GCash_logo'}}.png" />
              <view a:if="{{item.quantity>1}}" class="history-quantity">
                {{item.quantity}}
              </view>
            </view>
            <view a:if="{{ activePicker == 2 }}" class="history-icon">
              <image mode="scaleToFill" src="../../assets/new-icons/gift.png" />
            </view>
            <view class="item-left" style="font-size:28rpx;">
              <view>
                <text class="item-left-title">
                  {{item.labStatus}}
                  <!-- {{ activePicker == 1 ? item.name : item.pay_channel}} -->
                </text>
              </view>
              <view class="status-left-color" style="font-size:26rpx;">
                <text> {{item.status_name }}</text>
              </view>
            </view>
            <view class="item-right status-right-color font-din-medium">
              <view>
                <text>{{item.coins}}</text>
              </view>
            </view>
          </view>
        </view>

      </view>
      <view a:if="{{!transactionHistoryList.length}}" class="no-more-data">
        <image mode="scaleToFill" src="../../assets/new-icons/norecord.svg" />
        <view>No Record</view>
      </view>
    </scroll-view>
  </view>
</view>

<!-- 分批提现组合订单列表详情弹窗 -->
<view class="mask-one {{openMergeOrderId?'show':''}}" onTap="offDetailsList">
</view>
<view class=" deposit-confirm-box {{openMergeOrderId?'show':''}}">
  <view class="transaction-box-title detail-title">
    <view>
      <image onTap="offDetailsList" mode="scaleToFill" src="../../assets/new-icons/back.svg" />
    </view>
    <view>Transactions</view>
    <view onTap="offDetailsList" class="detail-title-close">
      <image onTap="offDetailsList" mode="scaleToFill" src="../../assets/new-icons/close.png" />
    </view>
  </view>
  <view class="mergeOrder-detailList-head">
    <view class="mergeOrder-detailList-head-title">
      Withdrawal(P)
    </view>
    <view class="mergeOrder-detailList-head-amount">
      <text>{{withdrawalMergeOrderItem.suc_amount}}</text> /
      <text>{{withdrawalMergeOrderItem.total_amount}}</text>
    </view>
    <view class="mergeOrder-detailList-head-no">
      Combine Order ID:
      {{withdrawalMergeOrderItem.combine_order_no}}
    </view>
  </view>
  <view class="transaction-select-list-area">
    <scroll-view scroll-y="{{true}}" onScrollToLower="getWithdrawalMergeOrders">
      <view class="history-times">
        <view a:for="{{withdrawalMergeOrderList}}" a:for-item="item" class="
              history-item 
              {{item.color}}
            " a:key="*this" onTap="openDetail" data-item="{{item}}">
          <view class="history-icon">
            <image
              a:if="{{ item.sys_remark == 'Adjustment' }}"
              mode="scaleToFill"
              src="../../assets/new-icons/gift.png"
            />
            <image a:else mode="scaleToFill" src="../../assets/new-icons/{{item.payment_method != 'Gcash' ? 'Maya_Logo' : 'GCash_logo'}}.png" />
          </view>
          <view class="item-left" style="font-size:28rpx;">
            <view>
              <text class="item-left-title">
                {{item.labStatus}}
              </text>
            </view>
            <view class="status-left-color" style="font-size:26rpx;">
              <text> {{item.status_name }}</text>
            </view>
          </view>
          <view class="item-right status-right-color font-din-medium">
            <view>
              <text>{{item.coins}}</text>
            </view>
          </view>
        </view>
      </view>
      <view a:if="{{!withdrawalMergeOrderList.length}}" class="no-more-data">
        <image mode="scaleToFill" src="../../assets/new-icons/norecord.svg" />
        <view>No Record</view>
      </view>
    </scroll-view>
  </view>
</view>


<!-- 交易记录详情 -->
<view class="mask-layer-4 mask {{showDetailMask?'show':''}}" onTap="offDetail">
</view>
<view class="deposit-confirm-box {{showDetailMask?'show':''}}">
  <view class="transaction-box-title detail-title">
    <view>
      <image onTap="offDetail" mode="scaleToFill" src="../../assets/new-icons/back.svg" />
    </view>
    <view class="detail-title-center">Detail</view>
    <view onTap="offDetail" class="detail-title-close">
      <image onTap="offDetail" mode="scaleToFill" src="../../assets/new-icons/close.png" />
    </view>
  </view>
  <view class="deposit-confim-main">
    <!-- deposit -->
    <view a:if="{{ activePicker == 0 }}" class="detail-main">
      <view class="detail-main-title">
        <view class="logo-background">
          <image
            a:if="{{showDetailInfo.pay_channel =='Adjustment'}}"
            mode="scaleToFill"
            src="../../assets/new-icons/gift.png"
          />
          <image a:else mode="scaleToFill" src="../../assets/new-icons/{{showDetailInfo.payment_method != 'Gcash' ? 'Maya_Logo' : 'GCash_logo'}}.png" />
        </view>
        <text>{{showDetailInfo.labStatus}}</text>
      </view>
      <view class="detail-banlance">{{showDetailInfo.amount}}</view>
      <view a:if="{{showDetailInfo.pay_channel !='Adjustment'}}" class="detail-type {{showDetailInfo.color}}">
        <!--           {{(showDetailInfo.recharge_status == 1)? 'success' : ''}}
        {{(showDetailInfo.recharge_status == 2 || showDetailInfo.recharge_status == 8 || showDetailInfo.recharge_status == 7) ? 'waiting' : ''}}
        {{(showDetailInfo.recharge_status == 3 || showDetailInfo.recharge_status == 4 || showDetailInfo.recharge_status == 5)? 'failure' : ''}} -->
        <view class="detail-type-name">
          {{showDetailInfo.status_name}}
        </view>
      </view>
      <view style="height: 48rpx;" class="block-height"></view>
      <view class="detail-line"></view>
      <view class="detail-lists">
        <view a:if="{{showDetailInfo.pay_channel !='Adjustment'}}" class="detail-item detail-topline">
          Payment Method
          <view class="detail-item-right">{{showDetailInfo.payment_method}}</view>
        </view>
        <view class="detail-item">
          Creation Time
          <view class="detail-item-right">{{showDetailInfo.created_at}}</view>
        </view>
        <view class="detail-item">
          Order ID
          <view style="font-size:24rpx;" class="detail-item-right">
            {{showDetailInfo.pay_serial_no}}
          </view>
          <image
            onTap="handleCopy"
            data-text="{{showDetailInfo.pay_serial_no}}"
            mode="scaleToFill"
            src="../../assets/new-icons/copy.svg"
          />
        </view>
        <view a:if="{{showDetailInfo.pay_channel =='Adjustment'}}" class="detail-item">
          Note
          <view class="detail-item-right">{{showDetailInfo.remark}}</view>
        </view>
      </view>
    </view>
    <!-- withdrawal -->
    <view a:if="{{ activePicker == 1 }}" class="detail-main">
      <view class="detail-main-title">
        <view class="logo-background">
          <image
            a:if="{{showDetailInfo.sys_remark == 'Adjustment'}}"
            mode="scaleToFill"
            src="../../assets/new-icons/gift.png"
          />
          <image a:else mode="scaleToFill" src="../../assets/new-icons/{{showDetailInfo.payment_method != 'Gcash' ? 'Maya_Logo' : 'GCash_logo'}}.png" />
        </view>
        <text>{{showDetailInfo.labStatus}}</text>
      </view>
      <view class="detail-banlance">-{{showDetailInfo.total_amount}}</view>
      <view a:if="{{showDetailInfo.sys_remark != 'Adjustment'}}" class="detail-type">
        <view class="
          {{showDetailInfo.ship_status == 1 ? 'pending' : ''}}
          {{showDetailInfo.ship_status == 2 ? 'success' : ''}}
          {{showDetailInfo.ship_status == 3 ? 'failure' : ''}}
          {{(showDetailInfo.ship_status == 4 || showDetailInfo.ship_status == 5) ? 'waiting' : ''}}
          ">
          {{showDetailInfo.status_name}}
        </view>
      </view>
      <view style="height: 48rpx;" class="block-height"></view>
      <view class="detail-line"></view>
      <view class="detail-lists">
        <view a:if="{{showDetailInfo.sys_remark != 'Adjustment'}}" class="detail-item">
          Payment Method
          <view class="detail-item-right">{{showDetailInfo.payment_method}}</view>
        </view>
        <view a:if="{{showDetailInfo.sys_remark != 'Adjustment'}}" class="detail-item">
          Account Number
          <view class="detail-item-right">{{showDetailInfo.account_no}}</view>
        </view>

        <view class="detail-item">
          Creation Time
          <view class="detail-item-right">{{showDetailInfo.created_at}}</view>
        </view>
        <view class="detail-item">
          Order ID
          <view style="font-size:24rpx;" class="detail-item-right">
            {{showDetailInfo.order_no}}
          </view>
          <image
            onTap="handleCopy"
            data-text="{{showDetailInfo.order_no}}"
            mode="scaleToFill"
            src="../../assets/new-icons/copy.svg"
          />
        </view>
        <view a:if="{{showDetailInfo.sys_remark =='Adjustment'}}" class="detail-item">
          <text style="margin-right: 20rpx;">Note</text>
          <view class="detail-item-right">{{showDetailInfo.remark}}</view>
        </view>
      </view>
    </view>
    <!-- Reward -->
    <view a:if="{{ activePicker == 2 }}" class="detail-main">
      <view class="detail-main-title">
        <view class="logo-background">
          <image mode="scaleToFill" src="../../assets/new-icons/gift.png" />
        </view>
        <text>Reward</text>
      </view>
      <view class="detail-banlance">{{showDetailInfo.amount}}</view>
      <view style="height: 48rpx;" class="block-height"></view>
      <view class="detail-line"></view>
      <view class="detail-lists">
        <view class="detail-item detail-topline">
          Reward
          <view class="detail-item-right">{{showDetailInfo.status_name}}</view>
        </view>
        <view class="detail-item">
          Creation Time
          <view class="detail-item-right">{{showDetailInfo.updated_at}}</view>
        </view>
        <view class="detail-item">
          Order ID
          <view style="font-size:24rpx;" class="detail-item-right">
            {{showDetailInfo.game_code}}
          </view>
          <image
            onTap="handleCopy"
            data-text="{{showDetailInfo.game_code}}"
            mode="scaleToFill"
            src="../../assets/new-icons/copy.svg"
          />
        </view>
      </view>
    </view>
  </view>
</view>


<!-- 交易记录时间筛选小窗 -->
<view class="mask-one {{showSelectTime?'show':''}}" onTap="offTimerSelect">
</view>
<view class="deposit-confirm-box {{showSelectTime?'show':''}}">
  <view class="transaction-box-title detail-title">
    Select a Date
    <view class="detail-title-close">
      <image onTap="offTimerSelect" mode="scaleToFill" src="../../assets/new-icons/close.png" />
    </view>
  </view>
  <view class="deposit-confim-main times-statue-select">
    <radio-group class="radio-group" onChange="clickTimeLable">
      <label
        a:for="{{transactionDateArray}}"
        a:for-index="index"
        onTap="clickTimeLable"
        data-index="{{index}}"
        class="radio-lable"
      >
        <view >{{item.name}}</view>
        <radio
          checked="{{item.id === activeTimer}}"
          color="#AC1140"
          class="radio"
          value="{{item.id}}"
        />
      </label>
    </radio-group>
  </view>
  <view onTap="changeActiveTimer" class="price-continue active-continue">
    Confirm
  </view>
</view>

<!-- 交易记录条件筛选小窗 -->
<view class="mask-one {{showSelectStatus?'show':''}}" onTap="offStatusSelect">
</view>
<view class="deposit-confirm-box {{showSelectStatus?'show':''}}">
  <view class="transaction-box-title detail-title">
    Select Status
    <view class="detail-title-close">
      <image onTap="offStatusSelect" mode="scaleToFill" src="../../assets/new-icons/close.png" />
    </view>
  </view>
  <view class="deposit-confim-main times-statue-select">
    <!-- onChange="clickTimeLable" -->

    <!-- 充提 换成单选 -->
    <radio-group a:if="{{activePicker!=2}}" class="radio-group" onChange="clickStatusLable">
      <label a:for="{{transactionStatusArray}}" a:for-index="index" class="radio-lable">
        <view>{{item.name}}</view>
        <radio color="#AC1140" checked="{{item.checked}}" value="{{item.id}}" />
      </label>
    </radio-group>
    <!-- reward -->
    <checkbox-group a:else class="radio-group" onChange="clickStatusLable">
      <label a:for="{{transactionStatusArray}}" a:for-index="index" class="radio-lable">
        <view >{{item.name}}</view>
        <checkbox color="#AC1140" checked="{{item.checked}}" value="{{item.id}}" />
      </label>
    </checkbox-group>
  </view>
  <view onTap="changeActiveStatus" class="price-continue active-continue">
    Confirm
  </view>
</view>

<risk-dialog
  currentBet="{{currentBet}}"
  totalBet="{{totalBet}}"
  onCancel="handleRiskCancel"
  onConfirm="handleRiskConfirm"
  ref="riskDialogRef"
/>

<help-dialog ref="saveHelpDialogRef">
  <help-dialog-title>Game maintenance</help-dialog-title>
  <help-dialog-content>
    <view class="mb-16">{{ maintenanceContent }}</view>
  </help-dialog-content>
  <help-dialog-action>
    <f-btn size="small" onTap="closeHelpDialog">OK</f-btn>
  </help-dialog-action>
</help-dialog>