# GCash Mini-Game 项目

基于支付宝小程序框架开发的 GCash 平台小游戏应用。

## 📋 项目概述

本项目是一个集成了游戏、充值、提现、交易记录等功能的小程序应用，支持多环境配置和数据管理优化。

## 🏗️ 项目结构

```
gcash-minigame/
├── pages/              # 应用页面
│   ├── game/          # 游戏相关页面
│   └── launch/        # 启动页面
├── utils/             # 工具函数
│   ├── config.js      # 环境配置文件
│   ├── http.js        # HTTP 请求工具
│   └── config-test.js # 配置测试工具
├── components/        # 可复用组件
├── assets/           # 静态资源
├── scripts/          # 脚本工具
│   └── switch-env.js # 环境切换脚本
└── docs/             # 项目文档
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone http://git.atm27.com/pokerpluscocos-web/gcashminiprogram.git
cd gcashminiprogram

# 安装依赖（如果有）
npm install
```

### 2. 环境配置

在 `utils/config.js` 中设置环境：

```javascript
// 修改环境配置：'dev' | 'prod' | 'test'
const ENVIRONMENT = "dev";
```

### 3. 上线前切换

```bash
# 方法一：手动修改 utils/config.js 中的 ENVIRONMENT
# 方法二：使用脚本切换
node scripts/switch-env.js prod
```

## 🔧 核心功能

### 环境配置管理

- ✅ 统一管理所有环境的 URL 配置
- ✅ 支持开发、测试、生产环境一键切换
- ✅ 集中管理应用固定配置参数
- ✅ 自动化切换工具

### 交易记录数据优化

- ✅ 按接口类别分别存储三个数据来源（充值、提现、奖励）
- ✅ 避免切换类型时数据混乱和丢失
- ✅ 提高数据管理的清晰度和可维护性
- ✅ 保持向后兼容性，视图层无需修改

### 下载引导浮标功能

- ✅ 支持后台配置控制显示与否
- ✅ 支持自定义提示内容和位置
- ✅ 使用 SVG 图标，静态显示无干扰
- ✅ 智能避开其他浮动元素，优化布局
- ✅ 点击可跳转到指定链接或显示提示

## 📊 重构成果

### 1. 环境配置重构 ✅

**目标**: 简化上线前的环境切换流程

**成果**:

- 提取了 `list.js`、`http.js`、`launch.js` 中的所有 URL 配置
- 创建统一的 `utils/config.js` 配置文件
- 支持 dev/prod/test 三种环境
- 提供自动化切换脚本

**影响文件**: 3 个核心文件 + 4 个新增文件

### 2. 交易记录重构 ✅

**目标**: 优化交易记录数据结构和管理

**成果**:

- 将混合存储改为按类型分别存储
- 实现数据隔离和持久化
- 减少重复网络请求
- 提升用户体验流畅度

**影响范围**: 交易记录相关功能

### 3. 下载引导浮标功能 ✅

**目标**: 增加用户下载引导，提升应用转化率

**成果**:

- 新增可配置的下载引导浮标
- 支持后台动态控制显示和内容
- 提供 4 种位置选择和动画效果
- 集成跳转和提示功能

**影响文件**: 3 个页面文件 + 1 个新增文档

## 🌍 环境配置详情

### 支持的环境

| 环境     | 标识   | 说明               |
| -------- | ------ | ------------------ |
| 开发环境 | `dev`  | 预发环境，默认环境 |
| 生产环境 | `prod` | 正式线上环境       |
| 测试环境 | `test` | 测试环境           |

### 配置参数对照表

| 参数名               | 开发环境                                      | 生产环境                                |
| -------------------- | --------------------------------------------- | --------------------------------------- |
| `baseUrl`            | `https://pre.nustaronline.vip/`               | `https://io.nustargame.com/`            |
| `baseUrl_common`     | `https://pre.nustaronline.vip/common/`        | `https://io.nustargame.com/common/`     |
| `baseUrl_avt`        | `https://pre.nustaronline.vip/avt/`           | `https://io.nustargame.com/avt/`        |
| `wwwUrl`             | `https://jumpminig.nustaronline.vip/`             | ` https://jumpminig.nustargame.com/`         |
| `assetsUrl`          | `https://uat-nustar-static.nustaronline.vip/` | `https://nustar-static.nustargame.com/` |
| `geoUrl`             | `https://pre-geo.nustaronline.vip:4433/geo`   | `https://geo.nustargame.com`            |
| `enableIpValidation` | `false`                                       | `true`                                  |
| `enableDebugMode`    | `true`                                        | `false`                                 |

### 功能开关说明

#### IP 验证开关 (`enableIpValidation`)

- **开发环境**: `false` - 跳过 IP 验证，加快开发调试
- **生产环境**: `true` - 启用 IP 验证，确保地理位置限制
- **影响**: 控制启动页面是否执行 IP 地理位置验证

#### 调试模式开关 (`enableDebugMode`)

- **开发环境**: `true` - 启用调试功能
- **生产环境**: `false` - 关闭调试功能
- **影响**:
  - 控制是否强制开启首充状态
  - 控制是否使用模拟游戏类型数据

## 📱 交易记录数据结构

### 重构前（问题）

```javascript
// 所有类型混合存储，切换时数据丢失
transactionHistoryList: [],        // 混合数据
copyTransactionHistoryList: [],    // 提现临时存储
acopyTransactionHistoryList: [],   // 充值/奖励临时存储
```

### 重构后（解决方案）

```javascript
// 按类别独立存储，数据隔离
transactionData: {
  topup: {      // 充值记录 (activePicker: 0)
    list: [],           // 原始数据
    groupedList: [],    // 分组数据
    pageNum: 1,         // 页码
    isNoMoreData: false // 状态
  },
  withdrawal: { // 提现记录 (activePicker: 1)
    list: [], groupedList: [], pageNum: 1, isNoMoreData: false
  },
  reward: {     // 奖励记录 (activePicker: 2)
    list: [], groupedList: [], pageNum: 1, isNoMoreData: false
  }
}
```

### 新增核心方法

- `getCurrentTransactionType()` - 获取当前激活的数据类型
- `getCurrentTransactionData()` - 获取当前激活的数据对象
- `updateCurrentTransactionData(updates)` - 更新当前数据
- `resetTransactionData(type)` - 重置指定类型数据
- `resetAllTransactionData()` - 重置所有数据

## 🔧 工具和脚本

### 配置测试工具

```javascript
import { testConfig, validateUrls } from "./utils/config-test.js";
testConfig(); // 测试当前配置
validateUrls(); // 验证 URL 格式
```

### 环境切换脚本

```bash
# 查看帮助
node scripts/switch-env.js help

# 切换环境
node scripts/switch-env.js dev   # 开发环境
node scripts/switch-env.js prod  # 生产环境
node scripts/switch-env.js test  # 测试环境
```

## ✅ 上线前检查清单

### 环境配置检查

- [ ] `utils/config.js` 中 `ENVIRONMENT` 设置为 `'prod'`
- [ ] 所有导入路径正确
- [ ] 测试登录功能
- [ ] 测试 API 请求
- [ ] 测试静态资源加载
- [ ] 测试地理位置验证

### 功能测试检查

- [ ] 交易记录类型切换测试
- [ ] 分页加载功能测试
- [ ] 筛选功能测试
- [ ] 充值/提现功能测试
- [ ] 游戏启动功能测试

## 📊 数据流程图

```
用户操作 → 类型判断 → 数据检查 → 显示/请求
    ↓           ↓           ↓           ↓
打开弹窗 → 充值(0) → 无数据 → 请求API → 存储到topup
切换类型 → 提现(1) → 有数据 → 直接显示 ← 从withdrawal读取
上拉加载 → 奖励(2) → 追加数据 → 请求下页 → 合并到reward
筛选条件 → 当前类型 → 重置数据 → 重新请求 → 更新对应类型
```

## 🎯 重构收益总结

### 1. **数据隔离与持久化**

- ✅ 每种交易类型数据独立存储
- ✅ 切换类型时保留已加载数据
- ✅ 避免数据混乱和意外覆盖

### 2. **性能优化**

- ✅ 减少重复网络请求
- ✅ 切换到已加载类型时即时显示
- ✅ 提升用户体验流畅度

### 3. **代码质量**

- ✅ 数据管理逻辑更清晰
- ✅ 减少重复代码和手动状态管理
- ✅ 提高可维护性和扩展性

### 4. **向后兼容**

- ✅ 视图层完全兼容，无需修改
- ✅ 现有数据绑定继续有效
- ✅ 平滑过渡，零风险部署

### 5. **配置管理优化**

- ✅ 简化操作：上线前只需修改一个变量
- ✅ 减少错误：避免遗漏修改某个文件的配置
- ✅ 统一管理：所有环境配置集中在一个文件
- ✅ 自动化：提供脚本工具自动切换

## 📁 相关文件

### 核心文件

- `pages/game/list.js` - 游戏列表页面，包含交易记录功能
- `pages/launch/launch.js` - 启动页面
- `utils/http.js` - HTTP 请求工具
- `utils/config.js` - 环境配置文件

### 工具文件

- `utils/config-test.js` - 配置测试工具
- `scripts/switch-env.js` - 环境切换脚本

### 文档文件

- `docs/README.md` - 项目文档索引
- `docs/下载引导浮标功能说明.md` - 下载引导浮标功能说明
- `docs/archive/README-交易记录重构.md` - 交易记录重构完成报告
- `docs/archive/README-环境配置重构.md` - 环境配置重构完成报告
- `docs/archive/交易记录重构说明.md` - 交易记录重构详细技术文档
- `docs/archive/环境配置说明.md` - 环境配置详细使用文档

## 🔮 后续优化建议

### 1. 数据缓存策略

- 考虑添加数据过期机制
- 实现智能缓存清理
- 添加数据刷新功能

### 2. 错误处理增强

- 网络错误时的状态恢复
- 数据加载失败的重试机制
- 用户友好的错误提示

### 3. 性能监控

- 添加数据加载性能监控
- 内存使用情况跟踪
- 用户行为分析

## 📞 技术支持

如遇问题，请参考：

1. `docs/README.md` - 项目文档索引
2. `docs/archive/环境配置说明.md` - 详细使用文档
3. `docs/archive/交易记录重构说明.md` - 交易记录技术文档
4. `utils/config-test.js` - 配置验证工具
5. 检查控制台错误信息

## 📈 项目状态

- **环境配置重构**: ✅ 已完成 (2025-07-08)
- **交易记录重构**: ✅ 已完成 (2025-07-08)
- **下载引导浮标**: ✅ 已完成 (2025-07-29)
- **向后兼容性**: ✅ 完全兼容
- **测试状态**: ✅ 已通过基础功能测试

## 🤝 贡献指南

1. 遵循现有的代码风格和结构
2. 修改配置时请更新相关文档
3. 提交前请运行完整的功能测试
4. 重要变更请先在开发环境验证

## 📄 许可证

本项目为内部项目，请遵循公司相关规定。

---

**最后更新**: 2025-07-29
**项目版本**: 1.0.183
**框架**: 支付宝小程序
**维护状态**: 🟢 活跃维护中
