/**
 * API 服务层
 * 提供业务级的 API 调用方法，进一步简化常用操作
 */

import requestManager from "./request-manager.js";
import { USER_API, PAYMENT_API, CONFIG_API, ACTIVITY_API, RECORD_API, GAME_API, SYSTEM_API, buildRequestOptions, buildApiWithId, TRANSACTION_API_MAP } from "./api-endpoints.js";
import { APP_GCASH, APP_VERSION, APP_DEVICE, APP_DEFAULT_DEVICE_ID } from "./config.js";
import { getDeviceId } from "./storage-manager.js";

/**
 * API 服务类
 */
class ApiService {
  constructor() {
    this.requestManager = requestManager;
  }

  // ========================================================================
  // 用户相关服务
  // ========================================================================

  /**
   * 用户登录
   * @param {string} authCode - 授权码
   * @param {Object} context - 页面上下文
   * @param {string} marketChannel - 市场渠道
   */
  async login(authCode, context, marketChannel = "") {
    const loginData = {
      app_package_name: APP_GCASH.packageName,
      appPackageName: APP_GCASH.packageName,
      app_version: APP_VERSION,
      appVersion: APP_VERSION,
      device_id: getDeviceId() || APP_DEFAULT_DEVICE_ID,
      gcash_auth_code: authCode,
      market_channel: marketChannel,
      login_type: "gcash",
      appChannel: APP_GCASH.channel,
      deviceModel: APP_DEVICE.model,
      deviceVersion: APP_DEVICE.version,
      sysLanguage: APP_DEVICE.language,
      sysTimezone: APP_DEVICE.timezone,
      source: APP_DEVICE.source,
      isNative: APP_DEVICE.isNative,
      telephoneCode: APP_GCASH.telephoneCode,
      registration_channel: APP_GCASH.registrationChannel,
      action: "/common/api/player/login",
    };

    return this.requestManager.login(authCode, context);
  }

  /**
   * 获取用户信息
   * @param {string} token - 用户token
   * @param {Object} context - 页面上下文
   */
  async getUserInfo(token, context) {
    const options = buildRequestOptions(USER_API.GET_INFO, { token }, { showLoading: true });
    return this.requestManager.request(options, context);
  }

  /**
   * 获取用户 KYC 信息
   * @param {string} token - 用户token
   * @param {Object} context - 页面上下文
   * @param {Object} options - 额外选项
   * @param {boolean} options.showLoading - 是否显示加载提示，默认false
   */
  async getUserKyc(token, context, options = {}) {
    const { showLoading = false } = options;
    const requestOptions = buildRequestOptions(USER_API.GET_KYC, { token }, { showLoading });
    return this.requestManager.request(requestOptions, context);
  }

  // ========================================================================
  // 支付相关服务
  // ========================================================================

  /**
   * 充值请求
   * @param {Object} params - 充值参数
   * @param {string} token - 用户token
   * @param {Object} context - 页面上下文
   */
  async deposit(params, token, context) {
    const depositData = {
      app_package_name: APP_GCASH.packageName,
      app_version: APP_VERSION,
      identifier: "gcashpay",
      ...params,
    };

    const options = buildRequestOptions(PAYMENT_API.BALANCE_ADD, depositData, { token, showLoading: true });

    return this.requestManager.request(options, context);
  }

  /**
   * 提现请求
   * @param {Object} params - 提现参数
   * @param {string} token - 用户token
   * @param {Object} context - 页面上下文
   */
  async withdraw(params, token, context) {
    const withdrawData = {
      app_package_name: APP_GCASH.packageName,
      app_version: APP_VERSION,
      quantity: 1,
      ...params,
    };

    const options = buildRequestOptions(PAYMENT_API.EXCHANGE, withdrawData, { token, showLoading: true });

    return this.requestManager.request(options, context);
  }

  /**
   * 提现风控检查
   * @param {string} token - 用户token
   * @param {Object} context - 页面上下文
   */
  async checkWithdrawRisk(token, context) {
    const options = buildRequestOptions(PAYMENT_API.WITHDRAW_RISK, { token }, { showLoading: true });

    return this.requestManager.request(options, context);
  }

  /**
   * 充值通知
   * @param {string} orderId - 订单ID
   * @param {number} status - 支付状态
   * @param {Object} context - 页面上下文
   */
  async sendRechargeNotify(orderId, status, context) {
    const notifyApi = buildApiWithId(PAYMENT_API.RECHARGE_NOTIFY, orderId);
    const options = buildRequestOptions(notifyApi, { acquirementStatus: status });

    return this.requestManager.request(options, context);
  }

  // ========================================================================
  // 配置相关服务
  // ========================================================================

  /**
   * 获取充值提现配置
   * @param {number} type - 配置类型 (1-充值, 2-提现)
   * @param {string} token - 用户token
   * @param {Object} context - 页面上下文
   */
  async getRechargeWithdrawConfig(type, token, context) {
    const configData = {
      token,
      accountType: "gcashpay",
      type,
    };

    const options = buildRequestOptions(CONFIG_API.RECHARGE_WITHDRAW_CONFIG, configData);
    return this.requestManager.request(options, context);
  }

  /**
   * 获取首充规则配置
   * @param {string} token - 用户token
   * @param {Object} context - 页面上下文
   */
  async getFirstRechargeRule(token, context) {
    const options = buildRequestOptions(CONFIG_API.FIRST_RECHARGE_RULE, { token });
    return this.requestManager.request(options, context);
  }

  /**
   * 获取下载引导配置
   * @param {string} token - 用户token
   * @param {Object} context - 页面上下文
   */
  async getDownloadGuideConfig(token, context) {
    const options = buildRequestOptions(CONFIG_API.DOWNLOAD_GUIDE_CONFIG,{},{token});
    return this.requestManager.request(options, context);
  }

  /**
   * 获取游戏类型配置
   * @param {Object} params - 请求参数
   * @param {Object} context - 页面上下文
   */
  async getGameTypeConfig(params, context) {
    const options = buildRequestOptions(CONFIG_API.GAME_TYPE_CONFIG, params);
    return this.requestManager.request(options, context);
  }

  // ========================================================================
  // 活动相关服务
  // ========================================================================

  /**
   * 获取活动枚举配置
   * @param {string} token - 用户token
   * @param {Object} context - 页面上下文
   */
  async getActivityEnum(token, context) {
    const options = buildRequestOptions(ACTIVITY_API.ADJUSTMENT_ENUM, { token });
    return this.requestManager.request(options, context);
  }

  /**
   * 获取横幅列表
   * @param {string} token - 用户token
   * @param {Object} context - 页面上下文
   */
  async getBannerList(token, context) {
    const options = buildRequestOptions(ACTIVITY_API.BANNER_LIST, { token });
    return this.requestManager.request(options, context);
  }

  // ========================================================================
  // 记录相关服务
  // ========================================================================

  /**
   * 获取交易记录
   * @param {number} type - 记录类型 (0-充值, 1-提现, 2-奖励)
   * @param {Object} params - 查询参数
   * @param {string} token - 用户token
   * @param {Object} context - 页面上下文
   */
  async getTransactionRecords(type, params, token, context) {
    const apiConfig = TRANSACTION_API_MAP[type];
    if (!apiConfig) {
      throw new Error(`Invalid transaction type: ${type}`);
    }

    const queryData = {
      token,
      page_number: 15,
      date_type: 3, // 固定为7天
      ...params,
    };

    const options = buildRequestOptions(apiConfig, queryData, { showLoading: true });
    return this.requestManager.request(options, context);
  }

  /**
   * 获取提现合并订单详情
   * @param {string} orderId - 订单ID
   * @param {Object} params - 查询参数
   * @param {string} token - 用户token
   * @param {Object} context - 页面上下文
   */
  async getWithdrawalMergeOrders(orderId, params, token, context) {
    const mergeOrderApi = buildApiWithId(RECORD_API.WITHDRAWAL_MERGE_ORDERS, orderId);
    const queryData = {
      token,
      page_number: 15,
      ...params,
    };

    const options = buildRequestOptions(mergeOrderApi, queryData, { showLoading: true });
    return this.requestManager.request(options, context);
  }

  // ========================================================================
  // 系统相关服务
  // ========================================================================

  /**
   * 获取跑马灯消息
   * @param {Object} context - 页面上下文
   */
  async getMarqueeList(context) {
    const options = buildRequestOptions(SYSTEM_API.MARQUEE_LIST);
    return this.requestManager.request(options, context);
  }

  /**
   * 返回游戏大厅 / 获取用户余额
   * @param {string} token - 用户token
   * @param {Object} context - 页面上下文
   * @param {Object} options - 额外选项
   * @param {boolean} options.showLoading - 是否显示加载提示，默认true
   * @param {boolean} options.retryOnError - 错误时是否重试，默认true
   */
  async backToHall(token, context, options = {}) {
    const { showLoading = true, retryOnError = true } = options;

    const requestOptions = buildRequestOptions(GAME_API.BACK_TO_HALL, { token }, { showLoading });

    try {
      const response = await this.requestManager.request(requestOptions, context);

      // 验证响应数据结构
      if (response.data && response.data.code === 200) {
        // 确保余额字段存在
        if (typeof response.data.balance === "undefined") {
          console.warn("backToHall API response missing balance field");
        }
      }

      return response;
    } catch (error) {
      console.error("backToHall API call failed:", error);

      // 如果启用重试且不是token相关错误，可以考虑重试
      if (retryOnError && !this._isTokenError(error)) {
        console.log("Attempting to retry backToHall request...");
        // 简单重试一次，不递归调用避免无限重试
        try {
          return await this.requestManager.request(requestOptions, context);
        } catch (retryError) {
          console.error("backToHall retry failed:", retryError);
          throw retryError;
        }
      }

      throw error;
    }
  }

  /**
   * 检查是否为token相关错误
   * @private
   */
  _isTokenError(error) {
    const tokenErrorCodes = [401, 400, 100010];
    return error && error.status && tokenErrorCodes.includes(error.status);
  }

  // ========================================================================
  // 批量操作
  // ========================================================================

  /**
   * 用户数据初始化 - 批量获取用户相关数据
   * @param {string} token - 用户token
   * @param {Object} context - 页面上下文
   */
  async initUserData(token, context) {
    try {
      const promises = [
        this.getUserKyc(token, context, { showLoading: false }),
        this.getRechargeWithdrawConfig(1, token, context), // 充值配置
        this.getRechargeWithdrawConfig(2, token, context), // 提现配置
        this.getDownloadGuideConfig(token, context),
        this.getActivityEnum(token, context),
      ];

      const results = await Promise.allSettled(promises);

      return {
        success: true,
        data: {
          kyc: results[0].status === "fulfilled" ? results[0].value : null,
          rechargeConfig: results[1].status === "fulfilled" ? results[1].value : null,
          withdrawConfig: results[2].status === "fulfilled" ? results[2].value : null,
          downloadGuide: results[3].status === "fulfilled" ? results[3].value : null,
          activityEnum: results[4].status === "fulfilled" ? results[4].value : null,
        },
        errors: results.filter((r) => r.status === "rejected").map((r) => r.reason),
      };
    } catch (error) {
      return { success: false, error };
    }
  }
}

// 创建单例实例
const apiService = new ApiService();

export { apiService, ApiService };
export default apiService;
