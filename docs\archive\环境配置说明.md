# 环境配置说明

## 概述

为了便于上线前的参数切换，我们将所有环境相关的 URL 配置统一提取到了 `utils/config.js` 文件中。

## 配置文件结构

### utils/config.js

```javascript
// 环境配置：'dev' | 'prod' | 'test'
const ENVIRONMENT = 'dev';

// 各环境配置
const CONFIG = {
  // 开发环境（预发）
  dev: { ... },

  // 生产环境
  prod: { ... },

  // 测试环境
  test: { ... }
};
```

## 支持的环境

1. **dev** - 开发环境（预发）
2. **prod** - 生产环境
3. **test** - 测试环境

## 配置参数说明

| 参数名               | 说明                | 示例                                          |
| -------------------- | ------------------- | --------------------------------------------- |
| `baseUrl`            | 基础 API 地址       | `https://pre.nustaronline.vip/`               |
| `baseUrl_common`     | 通用 API 地址       | `https://pre.nustaronline.vip/common/`        |
| `baseUrl_avt`        | AVT API 地址        | `https://pre.nustaronline.vip/avt/`           |
| `wwwUrl`             | 网站地址            | `https://jumpminig.nustaronline.vip/`             |
| `assetsUrl`          | 静态资源地址        | `https://uat-nustar-static.nustaronline.vip/` |
| `geoUrl`             | 地理位置验证地址    | `https://geo.nustargame.com`                  |
| `enableIpValidation` | 是否启用 IP 验证    | `true` / `false`                              |
| `enableDebugMode`    | 是否启用调试模式    | `true` / `false`                              |
| `debugAuthCode`      | 调试用固定 AuthCode | `string` / `null`                             |

## 上线前切换环境

### 方法一：修改 ENVIRONMENT 变量（推荐）

在 `utils/config.js` 文件中，只需要修改第 7 行的 `ENVIRONMENT` 变量：

```javascript
// 开发环境
const ENVIRONMENT = "dev";

// 生产环境
const ENVIRONMENT = "prod";

// 测试环境
const ENVIRONMENT = "test";
```

### 方法二：修改配置值

如果需要临时修改某个环境的配置，可以直接在 `CONFIG` 对象中修改对应环境的值。

## 受影响的文件

以下文件已经更新为使用统一配置：

1. **utils/http.js** - HTTP 请求工具
2. **pages/game/list.js** - 游戏列表页面
3. **pages/launch/launch.js** - 启动页面

## 迁移前后对比

### 迁移前

每个文件都有独立的 URL 配置，需要在多个地方手动切换：

```javascript
// pages/game/list.js
// dev
baseUrl: 'https://pre.nustaronline.vip/common/',
// 生产 上线切换
// baseUrl: 'https://io.nustargame.com/common/',
```

### 迁移后

所有文件统一从配置文件导入：

```javascript
// pages/game/list.js
import { baseUrl_common, baseUrl_avt, wwwUrl, assetsUrl } from "/utils/config.js";

Page({
  data: {
    baseUrl: baseUrl_common,
    baseUrl_avt: baseUrl_avt,
    wwwUrl: wwwUrl,
    assetsUrl: assetsUrl,
  },
});
```

## 注意事项

1. **确保导入路径正确**：所有导入都使用相对路径 `/utils/config.js`
2. **检查变量名映射**：确保导入的变量名与使用的变量名匹配
3. **测试所有环境**：切换环境后务必测试所有功能
4. **备份原配置**：建议在修改前备份原有的配置注释

## 快速切换检查清单

上线前请按以下步骤检查：

- [ ] 修改 `utils/config.js` 中的 `ENVIRONMENT` 变量
- [ ] 确认所有 URL 配置正确
- [ ] 测试登录功能
- [ ] 测试 API 请求
- [ ] 测试静态资源加载
- [ ] 测试地理位置验证

## 故障排除

如果遇到问题，请检查：

1. **导入路径**：确保 `import` 语句路径正确
2. **变量名**：确保导入和使用的变量名一致
3. **环境配置**：确认 `ENVIRONMENT` 变量设置正确
4. **URL 格式**：确认 URL 末尾的斜杠是否正确



# 配置参数提取说明

## 概述

本次重构将应用中的固定配置参数提取到了 `utils/config.js` 文件中，实现了统一管理，便于上线前的参数切换。

## 已提取的配置参数

### 1. 应用包名配置 (APP_GCASH)

```javascript
gcash: {
  packageName: "com.nustargame.gcash",
  appId: "2170020216334562",
  channel: "Gcash",
  telephoneCode: "+63",
  registrationChannel: "Gcash",
}
```

**使用位置：**

- `pages/game/list.js` - playLogin 函数
- `pages/game/list.js` - doDeposit 函数（充值/提现接口）
- `pages/game/list.js` - linkto 函数（appId）

### 2. 启动页面配置 (APP_LAUNCH)

```javascript
launch: {
  packageName: "com.playmate.playzone",
  channel: "Gcash",
  source: "mania",
}
```

**使用位置：**

- `pages/launch/launch.js` - getGameType 函数

### 3. 版本配置 (APP_VERSION)

```javascript
// 应用版本号 - 统一版本
appVersion: "1.0.183",
```

**使用位置：**

- `pages/game/list.js` - playLogin 函数
- `pages/game/list.js` - doDeposit 函数（充值/提现接口）
- `pages/launch/launch.js` - getGameType 函数

### 4. 设备相关配置 (APP_DEVICE)

```javascript
device: {
  model: "WEB",
  version: "WEB",
  language: "null",
  timezone: "null",
  source: "1",
  isNative: "0",
  terminal: 8,
}
```

**使用位置：**

- `pages/game/list.js` - playLogin 函数
- `pages/launch/launch.js` - 所有 API 请求的 headers
- `pages/game/list.js` - 所有 API 请求的 headers（部分已更新）

### 5. 默认设备 ID (APP_DEFAULT_DEVICE_ID)

```javascript
defaultDeviceId: "gcash2rmv3iln5qq0";
```

**使用位置：**

- `pages/game/list.js` - playLogin 函数作为 device_id 的默认值

## 已完成的更新

### ✅ utils/config.js

- 添加了 APP_CONFIG 配置对象
- 导出了所有应用固定配置参数

### ✅ pages/game/list.js

- 更新了 playLogin 函数中的所有硬编码值
- 更新了 doDeposit 函数中的充值/提现接口参数
- 更新了 linkto 函数中的 appId
- 更新了部分 API 请求的 terminal 值

### ✅ pages/launch/launch.js

- 更新了 getGameType 函数中的所有硬编码值
- 更新了部分 API 请求的 terminal 值

## 待完成的更新

### 🔄 pages/game/list.js - 剩余 terminal 更新

还有约 16 个 API 请求的 `terminal: 8` 需要更新为 `terminal: APP_DEVICE.terminal`

**涉及的函数：**

- getURLParams()
- getEnum()
- getRechargeRule()
- getBannerList()
- getNotification()
- getWithdrawalMergeOrders()
- getUserKyc()
- getRechangeNotis()
- backToHall()
- getRechargeWithdrawalList()
- getUserData()
- getTransactionHistoryList()
- doRedeem()
- getWithdrawalList()

### 🔄 utils/http.js

检查是否有需要更新的硬编码值

## 使用方法

### 导入配置

```javascript
import { APP_GCASH, APP_LAUNCH, APP_VERSION, APP_DEVICE, APP_DEFAULT_DEVICE_ID } from "/utils/config.js";
```

### 使用示例

```javascript
// 替换前
app_package_name: "com.nustargame.gcash",
app_version: "1.3.18",
terminal: 8,

// 替换后
app_package_name: APP_GCASH.packageName,
app_version: APP_VERSION,
terminal: APP_DEVICE.terminal,
```

## 上线前检查清单

- [ ] 确认所有硬编码值已替换为配置参数
- [ ] 测试登录功能
- [ ] 测试充值/提现功能
- [ ] 测试启动页面功能
- [ ] 测试所有 API 请求
- [ ] 验证 appId 跳转功能

## 优势

1. **统一管理**: 所有固定配置集中在一个文件中
2. **易于维护**: 修改配置只需要在一个地方
3. **减少错误**: 避免在多个文件中手动修改时出错
4. **版本控制**: 不同版本的配置可以清晰地管理
5. **环境切换**: 配合现有的环境配置系统，实现完整的配置管理

## 注意事项

1. 修改配置参数时，请确保所有使用该参数的地方都已更新
2. 上线前务必进行完整的功能测试
3. 建议保留原有的注释，便于理解配置的用途
4. 如果需要添加新的固定配置，请按照现有的结构添加到 APP_CONFIG 中

